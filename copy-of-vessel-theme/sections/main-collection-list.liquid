

{% liquid
  assign section_collections = collections
  assign max_items = 20

  case section.settings.layout_type
    when 'grid'
      assign classes = 'resource-list--grid'
      capture styles
        echo '--resource-list-column-gap-desktop: ' | append: section.settings.columns_gap | append: 'px;'
        echo '--resource-list-row-gap-desktop: ' | append: section.settings.rows_gap | append: 'px;'
        echo '--resource-list-columns: repeat(' | append: section.settings.columns | append: ', 1fr);'
        echo '--resource-list-columns-mobile: repeat(' | append: section.settings.mobile_columns | append: ', 1fr);'
        echo '--column-count-mobile: ' | append: section.settings.mobile_columns | append: ';'
      endcapture
    when 'bento'
      assign classes = 'resource-list--bento'
      capture styles
        echo '--resource-list-column-gap-desktop: ' | append: section.settings.bento_gap | append: 'px;'
        echo '--bento-gap: var(--resource-list-column-gap);'
      endcapture
    when 'carousel'
      assign classes = 'resource-list__carousel'
      capture styles
        echo '--resource-list-column-gap-desktop: ' | append: section.settings.columns_gap | append: 'px;'
        echo '--column-count: ' | append: section.settings.columns | append: ';'
        echo '--column-count-mobile: 1;'
      endcapture
  endcase
%}

<div class="section-background color-{{ section.settings.color_scheme }}"></div>

<div
  class="
    section
    section--{{ section.settings.section_width }}
    color-{{ section.settings.color_scheme }}
    section-resource-list
    spacing-style
    gap-style
    {% if section_collections == blank and request.design_mode == false %}
      in-onboarding-state
    {% endif %}
  "
  style="
    {% render 'spacing-style', settings: section.settings %}
    {% render 'gap-style', value: section.settings.gap %}
    {{ styles }}
  "
>
  <div class="section-resource-list__content">
    {%- content_for 'blocks' -%}
  </div>

  {% if section_collections == null %}
    {% for i in (1..max_items) %}
      {% assign section_collections = section_collections | append: ', ' %}
    {% endfor %}

    {% assign section_collections = section_collections | split: ',' %}
  {% endif %}

  {% capture list_items %}
    {% for collection in section_collections limit: max_items %}
      <div class="resource-list__item">
        {% content_for 'block', type: '_collection-card', id: 'static-collection-card', closest.collection: collection %}
      </div>
      {% unless forloop.last %}
        <!--@list/split-->
      {% endunless %}
    {% endfor %}
  {% endcapture %}

  {% liquid
    # Create an array from the list items to be used for different layout types
    assign list_items_array = list_items | strip | split: '<!--@list/split-->'
  %}

  <div
    class="
      resource-list
      {% if section.settings.layout_type == 'carousel' %}
        force-full-width
      {% endif %}
      {% if section.settings.carousel_on_mobile and section.settings.layout_type != 'carousel' %}
        hidden--mobile
      {% endif %}
      {{ classes }}
    "
    style="{{ styles }}"
    {% if section.settings.layout_type == 'grid' %}
      data-testid="collections-list-grid"
    {% endif %}
  >
    {% case section.settings.layout_type %}
      {% when 'grid' %}
        {{ list_items }}
      {% when 'bento' %}
        {% render 'bento-grid', items: list_items_array %}
      {% when 'carousel' %}
        {% render 'resource-list-carousel',
          ref: 'collectionListCarousel',
          slides: list_items_array,
          slide_count: max_items,
          settings: section.settings,
          slide_width_max: '450px'
        %}
      {% when 'editorial' %}
        {% render 'editorial-collection-grid', items: list_items_array %}
    {% endcase %}
  </div>

  {% if section.settings.layout_type != 'carousel' and section.settings.carousel_on_mobile %}
    {% liquid
      assign mobile_carousel_gap = section.settings.columns_gap
      if section.settings.layout_type == 'bento'
        assign mobile_carousel_gap = section.settings.bento_gap
      endif
    %}
    <div
      class="
        resource-list
        hidden--desktop
        resource-list__carousel
        force-full-width
      "
      style="
        --resource-list-column-gap-desktop: {{ mobile_carousel_gap }}px;
        --column-count: {{ section.settings.columns }};
        --column-count-mobile: 1;
      "
    >
      {% render 'resource-list-carousel',
        ref: 'collectionListCarouselMobile',
        slides: list_items_array,
        slide_count: max_items,
        settings: section.settings,
        slide_width_max: '450px'
      %}
    </div>
  {% endif %}
</div>

{% schema %}
{
  "name": "t:names.collection_list",
  "class": "ui-test-collection-list",
  "blocks": [
    {
      "type": "@theme"
    },
    {
      "type": "@app"
    },
    {
      "type": "text"
    },
    {
      "type": "icon"
    },
    {
      "type": "image"
    },
    {
      "type": "button"
    },
    {
      "type": "video"
    },
    {
      "type": "group"
    },
    {
      "type": "spacer"
    },
    {
      "type": "_divider"
    }
  ],
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "header",
      "content": "t:content.cards_layout"
    },
    {
      "type": "select",
      "id": "layout_type",
      "label": "t:settings.layout_type",
      "options": [
        {
          "value": "grid",
          "label": "t:options.grid"
        },
        {
          "value": "carousel",
          "label": "t:options.carousel"
        },
        {
          "value": "bento",
          "label": "t:options.bento"
        },
        {
          "value": "editorial",
          "label": "t:options.editorial"
        }
      ],
      "default": "grid"
    },
    {
      "type": "checkbox",
      "id": "carousel_on_mobile",
      "label": "t:settings.carousel_on_mobile",
      "default": false,
      "visible_if": "{{ section.settings.layout_type != 'carousel' }}"
    },
    {
      "type": "range",
      "id": "columns",
      "label": "t:settings.columns",
      "min": 1,
      "max": 8,
      "step": 1,
      "default": 4,
      "visible_if": "{{ section.settings.layout_type == 'grid' or section.settings.layout_type == 'carousel' }}"
    },
    {
      "type": "select",
      "id": "mobile_columns",
      "label": "t:settings.mobile_columns",
      "options": [
        {
          "value": "1",
          "label": "t:options.one_number"
        },
        {
          "value": "2",
          "label": "t:options.two_number"
        }
      ],
      "default": "2",
      "visible_if": "{{ section.settings.layout_type == 'grid' and section.settings.carousel_on_mobile == false }}"
    },
    {
      "type": "range",
      "id": "columns_gap",
      "label": "t:settings.horizontal_gap",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 8,
      "visible_if": "{{ section.settings.layout_type == 'grid' or section.settings.layout_type == 'carousel' }}"
    },
    {
      "type": "range",
      "id": "bento_gap",
      "label": "t:settings.gap",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 8,
      "visible_if": "{{ section.settings.layout_type == 'bento' }}"
    },
    {
      "type": "range",
      "id": "rows_gap",
      "label": "t:settings.vertical_gap",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 8,
      "visible_if": "{{ section.settings.layout_type == 'grid'}}"
    },
    {
      "type": "range",
      "id": "max_collections",
      "label": "t:settings.collection_count",
      "min": 1,
      "max": 16,
      "step": 1,
      "default": 4,
      "visible_if": "{{ section.settings.layout_type == 'editorial' }}"
    },
    {
      "type": "header",
      "content": "t:content.carousel_navigation",
      "visible_if": "{{ section.settings.layout_type == 'carousel' or section.settings.carousel_on_mobile == true }}"
    },
    {
      "type": "select",
      "id": "icons_style",
      "label": "t:settings.icon",
      "options": [
        {
          "value": "arrow",
          "label": "t:options.arrows"
        },
        {
          "value": "chevron",
          "label": "t:options.chevrons"
        },
        {
          "value": "arrows_large",
          "label": "t:options.arrows_large"
        },
        {
          "value": "chevron_large",
          "label": "t:options.chevron_large"
        },
        {
          "value": "none",
          "label": "t:options.none"
        }
      ],
      "default": "arrow",
      "visible_if": "{{ section.settings.layout_type == 'carousel' or section.settings.carousel_on_mobile == true }}"
    },
    {
      "type": "select",
      "id": "icons_shape",
      "label": "t:settings.icon_background",
      "options": [
        {
          "value": "none",
          "label": "t:options.none"
        },
        {
          "value": "circle",
          "label": "t:options.circle"
        },
        {
          "value": "square",
          "label": "t:options.square"
        }
      ],
      "default": "none",
      "visible_if": "{{ section.settings.icons_style != 'none' and section.settings.layout_type == 'carousel' or section.settings.carousel_on_mobile == true }}"
    },
    {
      "type": "header",
      "content": "t:content.section_layout"
    },
    {
      "type": "select",
      "id": "section_width",
      "label": "t:settings.width",
      "options": [
        {
          "value": "page-width",
          "label": "t:options.page"
        },
        {
          "value": "full-width",
          "label": "t:options.full"
        }
      ],
      "default": "page-width"
    },
    {
      "type": "range",
      "id": "gap",
      "label": "t:settings.gap",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 12
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:settings.color_scheme",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    }
  ]
}
{% endschema %}
