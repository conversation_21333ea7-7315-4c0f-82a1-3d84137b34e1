

<div class="section-background color-{{ section.settings.color_scheme }}"></div>
<div class="section section--page-width color-{{ section.settings.color_scheme }}">
  <div
    class="spacing-style search-page__header"
    style="
      --horizontal-alignment: {{ section.settings.alignment }};
      {% render 'spacing-style', settings: section.settings %}
    "
  >
    {% assign heading_text = 'content.search' | t %}

    {% if search.performed %}
      {% assign heading_text = 'content.search_results' | t %}
    {% endif %}

    {% capture heading_text %}
      <h3>{{ heading_text }}</h3>
    {% endcapture %}

    {% content_for 'block', id: 'heading', type: '_heading', text: heading_text %}
    {% content_for 'block', id: 'search', type: '_search-input' %}
  </div>
</div>

{% schema %}
{
  "name": "t:names.search",
  "settings": [
    {
      "type": "select",
      "id": "alignment",
      "label": "t:settings.alignment",
      "options": [
        {
          "value": "flex-start",
          "label": "t:options.left"
        },
        {
          "value": "center",
          "label": "t:options.center"
        },
        {
          "value": "flex-end",
          "label": "t:options.right"
        }
      ],
      "default": "flex-start"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:settings.color_scheme",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    }
  ]
}
{% endschema %}
