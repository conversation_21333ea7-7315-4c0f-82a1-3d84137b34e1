{% comment %}
  Guarantees Section - Customizable guarantee cards with icons
  Features configurable text, colors, fonts, and icons
{% endcomment %}

<div class="section-background color-{{ section.settings.color_scheme }}"></div>
<div
  class="
    guarantees-section spacing-style section section--{{ section.settings.section_width }} color-{{ section.settings.color_scheme }}
  "
  style="
    {% render 'spacing-style', settings: section.settings %}
    --guarantees-bg-color: {{ section.settings.background_color | default: 'rgba(var(--color-background-secondary), 1)' }};
    --guarantees-heading-color: {{ section.settings.heading_color | default: 'rgba(var(--color-foreground), 1)' }};
    --guarantees-card-bg: {{ section.settings.card_background_color | default: 'rgba(var(--color-background), 1)' }};
    --guarantees-card-title-color: {{ section.settings.card_title_color | default: 'rgba(var(--color-foreground), 1)' }};
    --guarantees-card-text-color: {{ section.settings.card_text_color | default: 'rgba(var(--color-foreground), 0.75)' }};
    --guarantees-icon-size: {{ section.settings.icon_size | default: 60 }}px;
    background: var(--guarantees-bg-color);
  "
>
  {% if section.settings.heading != blank %}
    <div class="guarantees-section__header">
      <h2 class="guarantees-section__heading">{{ section.settings.heading }}</h2>
      {% if section.settings.description != blank %}
        <div class="guarantees-section__description">{{ section.settings.description }}</div>
      {% endif %}
    </div>
  {% endif %}

  <div class="guarantees-section__container">
    {% for block in section.blocks %}
      {% if block.type == 'guarantee_item' %}
        <div class="guarantees-section__item" {{ block.shopify_attributes }}>
          <div class="guarantees-section__card">
            {% if block.settings.icon != blank or block.settings.icon_emoji != blank %}
              <div class="guarantees-section__icon" style="background-color: {{ block.settings.icon_background_color | default: '#4CAF50' }};">
                {% if block.settings.icon_emoji != blank %}
                  {{ block.settings.icon_emoji }}
                {% elsif block.settings.icon != blank %}
                  {% render 'icon', icon: block.settings.icon %}
                {% endif %}
              </div>
            {% endif %}
            
            {% if block.settings.title != blank %}
              <h3 class="guarantees-section__title">{{ block.settings.title }}</h3>
            {% endif %}
            
            {% if block.settings.description != blank %}
              <div class="guarantees-section__text">{{ block.settings.description }}</div>
            {% endif %}
          </div>
        </div>
      {% endif %}
    {% endfor %}
  </div>
</div>

{% stylesheet %}
  .guarantees-section {
    text-align: center;
  }

  .guarantees-section__header {
    margin-bottom: 3rem;
  }

  .guarantees-section__heading {
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--guarantees-heading-color);
    margin-bottom: 1rem;
    line-height: 1.2;
  }

  .guarantees-section__description {
    font-size: 1.125rem;
    color: var(--guarantees-card-text-color);
    max-width: 600px;
    margin: 0 auto;
  }

  .guarantees-section__container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
  }

  .guarantees-section__card {
    background: var(--guarantees-card-bg);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .guarantees-section__card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  .guarantees-section__icon {
    width: var(--guarantees-icon-size);
    height: var(--guarantees-icon-size);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    color: white;
  }

  .guarantees-section__icon svg {
    width: 24px;
    height: 24px;
    fill: currentColor;
  }

  .guarantees-section__title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--guarantees-card-title-color);
    margin-bottom: 1rem;
    line-height: 1.3;
  }

  .guarantees-section__text {
    color: var(--guarantees-card-text-color);
    line-height: 1.6;
    flex-grow: 1;
  }

  /* Mobile responsiveness */
  @media screen and (max-width: 768px) {
    .guarantees-section__heading {
      font-size: 2rem;
      padding: 0 1rem;
    }

    .guarantees-section__container {
      grid-template-columns: 1fr;
      gap: 1.5rem;
      padding: 0 1rem;
    }

    .guarantees-section__card {
      padding: 1.5rem;
    }

    .guarantees-section__icon {
      width: 50px;
      height: 50px;
      font-size: 1.25rem;
    }

    .guarantees-section__title {
      font-size: 1.125rem;
    }
  }

  @media screen and (max-width: 480px) {
    .guarantees-section__heading {
      font-size: 1.75rem;
    }

    .guarantees-section__container {
      padding: 0 0.75rem;
    }

    .guarantees-section__card {
      padding: 1.25rem;
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "Guarantees",
  "tag": "section",
  "class": "guarantees-wrapper section-wrapper",
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "label": "Main heading",
      "default": "Your Peace of Mind Guarantee"
    },
    {
      "type": "textarea",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Section background color"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color"
    },
    {
      "type": "color",
      "id": "card_background_color",
      "label": "Card background color"
    },
    {
      "type": "color",
      "id": "card_title_color",
      "label": "Card title color"
    },
    {
      "type": "color",
      "id": "card_text_color",
      "label": "Card text color"
    },
    {
      "type": "header",
      "content": "Icon settings"
    },
    {
      "type": "range",
      "id": "icon_size",
      "label": "Icon size",
      "min": 40,
      "max": 80,
      "step": 5,
      "unit": "px",
      "default": 60
    },
    {
      "type": "header",
      "content": "Section settings"
    },
    {
      "type": "select",
      "id": "section_width",
      "label": "Section width",
      "options": [
        {
          "value": "page-width",
          "label": "Page width"
        },
        {
          "value": "full-width",
          "label": "Full width"
        }
      ],
      "default": "full-width"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "label": "Color scheme",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "1",
          "label": "Scheme 1"
        },
        {
          "value": "2",
          "label": "Scheme 2"
        }
      ],
      "default": "1"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "Top padding",
      "min": 0,
      "max": 120,
      "step": 4,
      "unit": "px",
      "default": 80
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "Bottom padding",
      "min": 0,
      "max": 120,
      "step": 4,
      "unit": "px",
      "default": 80
    }
  ],
  "blocks": [
    {
      "type": "guarantee_item",
      "name": "Guarantee Item",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "180-Day Money-Back Guarantee"
        },
        {
          "type": "textarea",
          "id": "description",
          "label": "Description",
          "default": "Love your orthotics or get your money back. No questions asked. We're confident you'll feel the difference."
        },
        {
          "type": "text",
          "id": "icon_emoji",
          "label": "Icon (emoji)",
          "default": "✓",
          "info": "Use emoji for icon (e.g., ✓, 🚚, 👨‍⚕️, 💳)"
        },
        {
          "type": "color",
          "id": "icon_background_color",
          "label": "Icon background color",
          "default": "#4CAF50"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Guarantees",
      "blocks": [
        {
          "type": "guarantee_item",
          "settings": {
            "title": "180-Day Money-Back Guarantee",
            "description": "Love your orthotics or get your money back. No questions asked. We're confident you'll feel the difference.",
            "icon_emoji": "✓",
            "icon_background_color": "#4CAF50"
          }
        },
        {
          "type": "guarantee_item",
          "settings": {
            "title": "Free Shipping & Returns",
            "description": "Free shipping on all orders. Free returns if you need adjustments. We'll get it right.",
            "icon_emoji": "🚚",
            "icon_background_color": "#2196F3"
          }
        },
        {
          "type": "guarantee_item",
          "settings": {
            "title": "Designed by Podiatrists",
            "description": "Every pair is designed by licensed podiatrists with 25+ years of experience. Medical-grade quality.",
            "icon_emoji": "👨‍⚕️",
            "icon_background_color": "#FF9800"
          }
        },
        {
          "type": "guarantee_item",
          "settings": {
            "title": "FSA/HSA Eligible",
            "description": "Use your FSA/HSA funds. Combine with any coupon. Save money on medical-grade orthotics.",
            "icon_emoji": "💳",
            "icon_background_color": "#9C27B0"
          }
        }
      ]
    }
  ]
}
{% endschema %}
