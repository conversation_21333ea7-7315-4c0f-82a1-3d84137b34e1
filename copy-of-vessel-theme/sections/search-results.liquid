

<script
  src="{{ 'results-list.js' | asset_url }}"
  type="module"
></script>

<div class="section-background color-{{ section.settings.color_scheme }}"></div>
<results-list
  class="section product-grid-container spacing-style color-{{ section.settings.color_scheme }}"
  style="--padding-block-start: {{ section.settings.padding-block-start }}px; --padding-block-end: {{ section.settings.padding-block-end }}px;"
  section-id="{{ section.id }}"
>
  {% render 'skip-to-content-link', href: '#ResultsList', text: 'accessibility.skip_to_results_list' %}

  <div
    class="collection-wrapper grid gap-style"
  >
    {% paginate search.results by 24 %}
      {% if search.results_count > 0 %}
        {% content_for 'block', type: 'filters', id: 'filters', results: search, results_size: search.results_count %}
        {% assign products = search.results | where: 'object_type', 'product' %}
      {% else %}
        {% assign collection = settings.empty_state_collection | default: collections.all %}
        {% assign default_title = 'content.search_results_resource_products' | t %}
        {% assign title = settings.empty_state_collection.title | default: default_title %}
        {% assign products = collection.products %}
      {% endif %}

      {% capture children %}
        {% for product in products %}
          <li
            id="{{ section.id }}-{{ product.id }}"
            class="product-grid__item product-grid__item--{{ forloop.index0 }}"
            data-page="{{ paginate.current_page }}"
            data-product-id="{{ product.id }}"
            ref="cards[]"
          >
            {% content_for 'block', type: '_product-card', id: 'product-card', closest.product: product %}
          </li>
        {% endfor %}
      {% endcapture %}

      {% render 'product-grid',
        section: section,
        children: children,
        products: products,
        paginate: paginate,
        title: title
      %}
    {% endpaginate %}
  </div>
</results-list>

{% schema %}
{
  "name": "t:names.search_results",
  "settings": [
    {
      "type": "paragraph",
      "content": "t:content.edit_empty_state_collection_in_theme_settings"
    },
    {
      "type": "select",
      "id": "layout_type",
      "label": "t:settings.type",
      "options": [
        {
          "value": "grid",
          "label": "t:options.grid"
        },
        {
          "value": "organic",
          "label": "t:options.editorial"
        }
      ],
      "default": "grid"
    },
    {
      "type": "select",
      "id": "product_card_size",
      "label": "t:settings.card_size",
      "options": [
        {
          "value": "small",
          "label": "t:options.small"
        },
        {
          "value": "medium",
          "label": "t:options.medium"
        },
        {
          "value": "large",
          "label": "t:options.large"
        },
        {
          "value": "extra-large",
          "label": "t:options.extra_large"
        }
      ],
      "default": "medium",
      "visible_if": "{{ section.settings.layout_type == 'grid' }}"
    },
    {
      "type": "select",
      "id": "mobile_product_card_size",
      "label": "t:settings.mobile_card_size",
      "options": [
        {
          "value": "small",
          "label": "t:options.small"
        },
        {
          "value": "large",
          "label": "t:options.large"
        }
      ],
      "default": "small"
    },
    {
      "type": "header",
      "content": "t:content.layout"
    },
    {
      "type": "select",
      "id": "product_grid_width",
      "label": "t:settings.width",
      "options": [
        {
          "value": "centered",
          "label": "t:options.page"
        },
        {
          "value": "full-width",
          "label": "t:options.full"
        }
      ],
      "default": "centered"
    },
    {
      "type": "checkbox",
      "id": "full_width_on_mobile",
      "label": "t:settings.full_width_on_mobile",
      "default": true,
      "visible_if": "{{ section.settings.product_grid_width != 'full-width' }}"
    },
    {
      "type": "range",
      "id": "columns_gap_horizontal",
      "label": "t:settings.horizontal_gap",
      "min": 0,
      "max": 50,
      "step": 1,
      "unit": "px",
      "default": 16
    },
    {
      "type": "range",
      "id": "columns_gap_vertical",
      "label": "t:settings.vertical_gap",
      "min": 0,
      "max": 50,
      "step": 1,
      "unit": "px",
      "default": 16
    },
    {
      "type": "range",
      "id": "padding-inline-start",
      "label": "t:settings.left_padding",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-inline-end",
      "label": "t:settings.right_padding",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "header",
      "content": "t:content.section_layout"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:settings.color_scheme",
      "default": "scheme-1"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top_padding",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 8
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom_padding",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 8
    }
  ],
  "presets": []
}
{% endschema %}
