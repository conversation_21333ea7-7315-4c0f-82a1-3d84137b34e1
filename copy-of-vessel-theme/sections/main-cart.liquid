

<cart-items-component
  class="cart-items-component"
  data-section-id="{{ section.id }}"
>
  <div class="section-background color-{{ section.settings.color_scheme }}"></div>
  <div
    class="section color-{{ section.settings.color_scheme }} section--{{ section.settings.section_width }}"
  >
    <div
      class="cart-page spacing-style{% if cart.empty? %} cart-page--empty{% endif %}"
      style="{% render 'spacing-style', settings: section.settings %}"
    >
      <div class="cart-page__title">
        {%- content_for 'block', id: 'cart-page-title', type: '_cart-title' %}
      </div>

      <div class="cart-page__items">
        {%- content_for 'block', id: 'cart-page-items', type: '_cart-products' %}
      </div>

      {%- unless cart.empty? -%}
        <div class="cart-page__summary">
          {%- content_for 'block', id: 'cart-page-summary', type: '_cart-summary' -%}
        </div>
      {%- endunless -%}

      <div class="cart-page__more-blocks">
        {%- content_for 'blocks' -%}
      </div>
    </div>
  </div>
</cart-items-component>

{% stylesheet %}
  .cart-page {
    --cart-font-size--2xs: var(--font-size--2xs);
    --cart-font-size--xs: var(--font-size--xs);
    --cart-font-size--sm: var(--font-size--sm);
    --cart-font-size--md: var(--font-size--md);
    --cart-font-size--2xl: var(--font-size--2xl);

    display: grid;
    grid-template-columns: 1fr;
    gap: 0 var(--padding-5xl);
  }

  .cart-page--empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .cart-page--empty .cart-page__title,
  .cart-page--empty .cart-page__more-blocks {
    margin-top: var(--margin-6xl);
  }

  .cart-page__more-blocks {
    width: 100%;
  }

  .cart-page--empty .cart-title {
    text-align: center;
  }

  .cart-page__main {
    grid-column: 1;
  }

  .cart-page__summary {
    padding-top: var(--padding-xl);
  }

  @media screen and (min-width: 750px) {
    .cart-page {
      grid-template-columns: 1fr min(50vw, var(--sidebar-width));
      grid-template-rows: min-content min-content 1fr;
    }

    .cart-page__summary {
      display: grid;
      height: 100%;
      grid-column: 2;
      grid-row: 1 / -1;
      align-self: stretch;
      grid-template-rows: subgrid;
      padding-top: 0;
      /* needed to support blurred effect from hero section */
      position: relative;
    }

    .section--page-width .cart-page:has(.cart__container--extend) {
      grid-column: 2 / 4;
      grid-template-columns: 1fr minmax(
          var(--sidebar-width),
          calc((100vw - var(--page-width)) / 2 + var(--sidebar-width))
        );
    }

    .cart__container--extend {
      height: 100%;
    }
  }

  @media screen and (min-width: 1400px) {
    .cart-page {
      grid-template-columns: 1fr var(--sidebar-width);
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.cart",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "blocks": [
    {
      "type": "@theme"
    },
    {
      "type": "@app"
    },
    {
      "type": "text"
    },
    {
      "type": "icon"
    },
    {
      "type": "image"
    },
    {
      "type": "button"
    },
    {
      "type": "video"
    },
    {
      "type": "group"
    },
    {
      "type": "spacer"
    }
  ],
  "settings": [
    {
      "type": "select",
      "id": "section_width",
      "label": "t:settings.width",
      "options": [
        {
          "value": "page-width",
          "label": "t:options.page"
        },
        {
          "value": "full-width",
          "label": "t:options.full"
        }
      ],
      "default": "page-width"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:settings.color_scheme",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    }
  ]
}
{% endschema %}
