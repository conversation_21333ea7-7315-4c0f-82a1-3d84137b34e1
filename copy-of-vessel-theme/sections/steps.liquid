{% comment %}
  Steps Section - How It Works
  Displays a series of steps with images, titles, and descriptions
{% endcomment %}

<div class="section-background color-{{ section.settings.color_scheme }}"></div>
<div
  class="
    steps-section spacing-style section section--{{ section.settings.section_width }} color-{{ section.settings.color_scheme }}
  "
  style="
    {% render 'spacing-style', settings: section.settings %}
    --steps-bg-color: {{ section.settings.background_color | default: '#fff' }};
    --steps-heading-color: {{ section.settings.heading_color | default: 'rgba(var(--color-foreground), 1)' }};
    --steps-text-color: {{ section.settings.text_color | default: 'rgba(var(--color-foreground), 0.75)' }};
    --steps-card-bg: {{ section.settings.card_background_color | default: 'rgba(var(--color-background), 1)' }};
    --steps-card-border: {{ section.settings.card_border_color | default: 'rgba(var(--color-border), 0.08)' }};
    --steps-step-number-color: {{ section.settings.step_number_color | default: 'rgba(var(--color-foreground), 0.75)' }};
    --steps-button-bg: {{ section.settings.button_background_color | default: '#00bcd4' }};
    --steps-button-text: {{ section.settings.button_text_color | default: '#ffffff' }};
    --steps-button-hover-bg: {{ section.settings.button_hover_background_color | default: '#00acc1' }};
    --steps-placeholder-bg: {{ section.settings.placeholder_background_color | default: 'linear-gradient(135deg, #e6f3ff 0%, #b3d9ff 50%, #87ceeb 100%)' }};
    --steps-placeholder-color: {{ section.settings.placeholder_text_color | default: '#4a90e2' }};
    background: var(--steps-bg-color);
  "
>
  {% if section.settings.heading != blank %}
    <div class="steps-section__header">
      <h2 class="steps-section__heading">{{ section.settings.heading }}</h2>
      {% if section.settings.description != blank %}
        <div class="steps-section__description">{{ section.settings.description }}</div>
      {% endif %}
    </div>
  {% endif %}

  <div class="steps-section__container">
    {% for block in section.blocks %}
      {% if block.type == 'step' %}
        <div class="steps-section__step" {{ block.shopify_attributes }}>
          <div class="steps-section__step-image">
            {% if block.settings.image != blank %}
              <img
                src="{{ block.settings.image | image_url: width: 400 }}"
                alt="{{ block.settings.image.alt | default: block.settings.title }}"
                width="400"
                height="300"
                loading="lazy"
              >
            {% else %}
              <div class="steps-section__placeholder-image"></div>
            {% endif %}
          </div>
          
          <div class="steps-section__step-content">
            {% if block.settings.step_number != blank %}
              <div class="steps-section__step-number">{{ block.settings.step_number }}</div>
            {% endif %}
            
            {% if block.settings.title != blank %}
              <h3 class="steps-section__step-title">{{ block.settings.title }}</h3>
            {% endif %}
            
            {% if block.settings.description != blank %}
              <div class="steps-section__step-description">{{ block.settings.description }}</div>
            {% endif %}
          </div>
        </div>
      {% endif %}
    {% endfor %}
  </div>

  {% if section.settings.button_label != blank and section.settings.button_link != blank %}
    <div class="steps-section__footer">
      <a 
        href="{{ section.settings.button_link }}" 
        class="steps-section__button button button--primary"
        {% if section.settings.button_new_tab %}target="_blank" rel="noopener"{% endif %}
      >
        {{ section.settings.button_label }}
      </a>
    </div>
  {% endif %}
</div>

{% stylesheet %}
  .steps-section {
    text-align: center;
  }

  .steps-section__header {
    margin-bottom: 3rem;
  }

  .steps-section__heading {
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--steps-heading-color);
    margin-bottom: 1rem;
    line-height: 1.2;
  }

  .steps-section__description {
    font-size: 1.125rem;
    color: var(--steps-text-color);
    max-width: 600px;
    margin: 0 auto;
  }

  .steps-section__container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
  }

  .steps-section__step {
    background: var(--steps-card-bg);
    border-radius: 16px;
    padding: 2rem 1.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--steps-card-border);
  }

  .steps-section__step:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
  }

  .steps-section__step-image {
    margin-bottom: 1.5rem;
    border-radius: 12px;
    overflow: hidden;
  }

  .steps-section__step-image img {
    width: 100%;
    height: 220px;
    object-fit: cover;
    border-radius: 12px;
  }

  .steps-section__placeholder-image {
    width: 100%;
    height: 220px;
    background: var(--steps-placeholder-bg);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--steps-placeholder-color);
    font-size: 1rem;
    font-weight: 500;
    position: relative;
    overflow: hidden;
  }

  .steps-section__placeholder-image::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234a90e2' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  }

  .steps-section__placeholder-image::after {
    content: "Image";
    position: relative;
    z-index: 1;
  }

  .steps-section__step-number {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--steps-step-number-color);
    margin-bottom: 0.5rem;
  }

  .steps-section__step-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--steps-heading-color);
    margin-bottom: 1rem;
    line-height: 1.3;
  }

  .steps-section__step-description {
    font-size: 1rem;
    color: var(--steps-text-color);
    line-height: 1.6;
  }

  .steps-section__footer {
    text-align: center;
    margin-top: 2rem;
  }

  .steps-section__button {
    background: var(--steps-button-bg);
    color: var(--steps-button-text);
    padding: 1rem 2.5rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.125rem;
    display: inline-block;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .steps-section__button:hover {
    background: var(--steps-button-hover-bg);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    color: var(--steps-button-text);
    text-decoration: none;
  }

  /* Mobile responsiveness */
  @media screen and (max-width: 768px) {
    .steps-section__container {
      grid-template-columns: 1fr;
      gap: 1.5rem;
      padding: 0 1rem;
    }

    .steps-section__heading {
      font-size: 2rem;
      padding: 0 1rem;
    }

    .steps-section__step {
      padding: 1.5rem 1rem;
    }

    .steps-section__step-image img,
    .steps-section__placeholder-image {
      height: 180px;
    }

    .steps-section__step-title {
      font-size: 1.25rem;
    }

    .steps-section__button {
      padding: 0.875rem 1.5rem;
      font-size: 1rem;
    }
  }

  @media screen and (min-width: 1200px) {
    .steps-section__container {
      grid-template-columns: repeat(3, 1fr);
      gap: 2.5rem;
    }

    .steps-section__step {
      padding: 2.5rem 2rem;
    }
  }

  /* Ensure proper spacing and layout */
  .steps-section .section {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  @media screen and (min-width: 768px) {
    .steps-section .section {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "Steps",
  "tag": "section",
  "class": "steps-wrapper section-wrapper",
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Get Custom Orthotics in 3 Easy Steps"
    },
    {
      "type": "textarea",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "header",
      "content": "Button"
    },
    {
      "type": "text",
      "id": "button_label",
      "label": "Button label",
      "default": "TAKE THE QUIZ"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "checkbox",
      "id": "button_new_tab",
      "label": "Open in new tab",
      "default": false
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#f8fbff"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color"
    },
    {
      "type": "color",
      "id": "card_background_color",
      "label": "Card background color"
    },
    {
      "type": "color",
      "id": "card_border_color",
      "label": "Card border color"
    },
    {
      "type": "color",
      "id": "step_number_color",
      "label": "Step number color"
    },
    {
      "type": "color",
      "id": "button_background_color",
      "label": "Button background color",
      "default": "#00bcd4"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "button_hover_background_color",
      "label": "Button hover background color",
      "default": "#00acc1"
    },
    {
      "type": "color",
      "id": "placeholder_text_color",
      "label": "Placeholder text color",
      "default": "#4a90e2"
    },
    {
      "type": "header",
      "content": "Section settings"
    },
    {
      "type": "select",
      "id": "section_width",
      "label": "Section width",
      "options": [
        {
          "value": "page-width",
          "label": "Page width"
        },
        {
          "value": "full-width",
          "label": "Full width"
        }
      ],
      "default": "page-width"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "label": "Color scheme",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "1",
          "label": "Scheme 1"
        },
        {
          "value": "2",
          "label": "Scheme 2"
        }
      ],
      "default": ""
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "Top padding",
      "min": 0,
      "max": 120,
      "step": 4,
      "unit": "px",
      "default": 48
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "Bottom padding",
      "min": 0,
      "max": 120,
      "step": 4,
      "unit": "px",
      "default": 48
    }
  ],
  "blocks": [
    {
      "type": "step",
      "name": "Step",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "text",
          "id": "step_number",
          "label": "Step number",
          "default": "Step 1"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Step title"
        },
        {
          "type": "textarea",
          "id": "description",
          "label": "Description",
          "default": "Step description goes here."
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Steps",
      "blocks": [
        {
          "type": "step",
          "settings": {
            "step_number": "Step 1",
            "title": "Take Assessment",
            "description": "Take 50 sec towards your pain-free life, and answer a few short questions."
          }
        },
        {
          "type": "step",
          "settings": {
            "step_number": "Step 2", 
            "title": "Get Impression Kit",
            "description": "Get Upstep's Impression Kit at your door. Imprint your feet, and send it back free of charge."
          }
        },
        {
          "type": "step",
          "settings": {
            "step_number": "Step 3",
            "title": "Receive Custom Orthotics",
            "description": "We'll create and ship your Upsteps within 12-18 business days of receiving your impression kit."
          }
        }
      ]
    }
  ]
}
{% endschema %}
