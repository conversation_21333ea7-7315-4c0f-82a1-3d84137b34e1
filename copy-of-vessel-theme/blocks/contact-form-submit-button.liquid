

<button
  type="submit"
  class="button submit-button size-style {{ block.settings.style_class }}"
  style="{% render 'size-style', settings: block.settings %}"
  {{ block.shopify_attributes }}
>
  {{ block.settings.label }}
</button>

{% stylesheet %}
  .submit-button {
    min-width: max-content;
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.submit_button",
  "tag": null,
  "settings": [
    {
      "type": "text",
      "id": "label",
      "label": "t:settings.label",
      "default": "t:text_defaults.contact_form_button_label"
    },
    {
      "type": "select",
      "id": "style_class",
      "label": "t:settings.style",
      "options": [
        {
          "value": "button",
          "label": "t:options.primary"
        },
        {
          "value": "button-secondary",
          "label": "t:options.secondary"
        }
      ],
      "default": "button"
    },
    {
      "type": "header",
      "content": "t:content.size"
    },
    {
      "type": "select",
      "id": "width",
      "label": "t:settings.width_desktop",
      "options": [
        {
          "value": "fit-content",
          "label": "t:options.fit_content"
        },
        {
          "value": "custom",
          "label": "t:options.custom"
        }
      ],
      "default": "fit-content"
    },
    {
      "type": "range",
      "id": "custom_width",
      "label": "t:settings.width",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "default": 100,
      "visible_if": "{{ block.settings.width == 'custom' }}"
    },
    {
      "type": "select",
      "id": "width_mobile",
      "label": "t:settings.width_mobile",
      "options": [
        {
          "value": "fit-content",
          "label": "t:options.fit_content"
        },
        {
          "value": "custom",
          "label": "t:options.custom"
        }
      ],
      "default": "fit-content"
    },
    {
      "type": "range",
      "id": "custom_width_mobile",
      "label": "t:settings.width",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "default": 100,
      "visible_if": "{{ block.settings.width_mobile == 'custom' }}"
    }
  ],
  "presets": []
}
{% endschema %}
