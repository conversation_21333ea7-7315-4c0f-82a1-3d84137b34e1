{%- doc -%}
  Renders the cart icon, which displays the number of items in the cart via a bubble.
{%- enddoc -%}
<cart-icon
  class="
    header-actions__cart-icon
    {% unless cart == empty %} header-actions__cart-icon--has-cart{% endunless %}
  "
  data-testid="cart-icon"
>
  <span
    class="svg-wrapper"
    aria-hidden="true"
  >
    {{ 'icon-cart.svg' | inline_asset_content }}
  </span>

  {% render 'cart-bubble', limit: 100, live_region: true, test_id: test_id %}
</cart-icon>

{% stylesheet %}
  cart-icon:has(.cart-bubble__text-count:empty) {
    --cart-bubble-size: 10px;
    --cart-bubble-top: 9px;
    --cart-bubble-right: 9px;

    .svg-wrapper {
      --cart-bubble-top: 4px;
      --cart-bubble-right: 4px;
    }
  }
{% endstylesheet %}
