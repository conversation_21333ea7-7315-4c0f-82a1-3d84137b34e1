{%- doc -%}
  Renders arrow controls for a slideshow component.
  Assumes arrows are placed on top of media.
  When icon shape is 'none', component uses mixed-blend-mode to ensure visibility.

  @param {string} [class] - The class name to apply to the slideshow-arrows component
  @param {string} [icon_style] - The style of the icon, defaults to 'arrow'
  @param {string} [icon_shape] - The shape of the icons background, defaults to 'none'
  @param {string} [arrows_position] - { 'left' | 'center' | 'right' } The position of the arrows, defaults to 'center'

  @example
  {%- render 'slideshow-arrows' -%}
{%- enddoc -%}

{%- liquid
  if arrows_position == null
    assign arrows_position = 'center'
  endif
-%}

<slideshow-arrows
  position="{{ arrows_position }}"
  shape="{{ icon_shape | default: 'none' }}"
  {% if class %}
    class="{{ class }}"
  {% endif %}
>
  {%- render 'slideshow-arrow', action: 'previous', icon_style: icon_style, icon_shape: icon_shape -%}
  {%- render 'slideshow-arrow', action: 'next', icon_style: icon_style, icon_shape: icon_shape -%}
</slideshow-arrows>
