{%- doc -%}
  Renders the search action button.

  @param {string} [style] - The style of the search action.
  @param {string} [class] - Additional classes for the search action.

  @example
  {% render 'search', style: 'default', class: 'custom-class' %}
{%- enddoc -%}

{% unless style == 'none' %}
  <search-button class="search-action{% if class != blank %} {{ class | strip }}{% endif %}">
    <button
      on:click="#search-modal/showDialog"
      class="button button-unstyled search-modal__button header-actions__action"
      aria-label="{{ 'accessibility.open_search_modal' | t }}"
    >
      <span
        aria-hidden="true"
        class="svg-wrapper"
      >
        {{ 'icon-search.svg' | inline_asset_content }}
      </span>
    </button>
  </search-button>
{% endunless %}

{% stylesheet %}
  .search-action {
    --search-border-radius: var(--style-border-radius-inputs);
    --search-border-width: var(--style-border-width-inputs);

    display: flex;
  }

  .header__column--center .search-action {
    width: auto;
    flex-grow: 1;
  }

  :is(.header__column--left, .header__column--center) .search-action {
    @media screen and (min-width: 750px) {
      margin-inline: calc(var(--padding-lg) * -1);
    }
  }

  .header__column--right .search-action {
    @media screen and (min-width: 750px) {
      margin-inline: calc(var(--gap-md) * -1) calc(var(--gap-xs) * -1);
    }
  }
{% endstylesheet %}
