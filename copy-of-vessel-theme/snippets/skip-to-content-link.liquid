{%- doc -%}
  Renders a skip to content link, visible on focus only.
  Parent element must have position: relative to ensure proper positioning.

  @param {string} href - The URL to skip to, usually an id like "#MainContent".
  @param {string} text - The text to display, in the form of a translation key for a locale file.

  @example
  {% render 'skip-to-content-link', href: '#MainContent', text: 'Skip to main content' %}
{%- enddoc -%}
<a
  class="skip-to-content-link button-secondary"
  href="{{ href }}"
>
  {{ text | t }}
</a>
