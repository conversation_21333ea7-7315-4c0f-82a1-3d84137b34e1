{%- doc -%}
  Renders the CSS variables for the `gap` styles needed for responsive scaling.
  Intended for use with the `gap-style` class.

  @param {number} value - The base or desktop gap value to use, in pixels.
  @param {string} [name] - The name of the CSS variable to set. Default: 'gap'
  @param {number} [scale_min] - Value above which gap scaling will be applied. Default: 20
  @param {boolean} [disable_scaling] - If true, disables scaling and outputs the original value.

  @example
  <div class="gap-style" style="{% render 'gap-style', value: block.settings.gap %}">
{%- enddoc -%}

{%- liquid
  assign min = scale_min | default: 24
  assign name = name | default: 'gap'
-%}

{%- if value != blank -%}
  {%- if disable_scaling != true and value > min -%}
    --{{ name }}: max({{ min }}px, calc(var(--gap-scale, 1.0) * {{ value }}px));
  {%- else -%}
    --{{ name }}: {{ value }}px;
  {%- endif -%}
{%- endif -%}
