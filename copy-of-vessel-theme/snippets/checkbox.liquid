{%- doc -%}
  Renders a checkbox input and label

  @param {string} id - input id attribute
  @param {string} name - input name attribute
  @param {string} value - input value attribute
  @param {string} label - label text
  @param {boolean} checked - whether the input is checked
  @param {string} events - event attributes for the input, e.g. 'on:click="/action"'
  @param {boolean} disabled - whether the input is disabled
  @param {string} [inputRef] - input ref attribute for use with component framework
  @param {string} [labelRef] - label ref attribute for use with component framework
  @param {boolean} [autofocus] - whether the input should be autofocused
{%- enddoc -%}
<div
  class="checkbox"
  {{ events }}
>
  <input
    type="checkbox"
    name="{{ name }}"
    value="{{ value }}"
    id="{{ id }}"
    class="checkbox__input"
    data-label="{{ label }}"
    {% if checked %}
      checked
    {% endif %}
    {% if disabled %}
      disabled
    {% endif %}
    {% if inputRef != blank %}
      ref="{{ inputRef }}"
    {% endif %}
    {% if autofocus %}
      autofocus
    {% endif %}
  >
  <label
    class="checkbox__label"
    for="{{ id }}"
    {% if labelRef != blank %}
      ref="{{ labelRef }}"
    {% endif %}
    role="checkbox"
  >
    {{ 'icon-checkmark.svg' | inline_asset_content }}
    <span class="checkbox__label-text">{{- label -}}</span>
  </label>
</div>
