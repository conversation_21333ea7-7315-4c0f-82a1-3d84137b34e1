{%- doc -%}
  Renders a localization component for the drawer.
  The <drawer-localization> component must be used inside a <header-drawer> component. It relies on event bindings from the parent <header-drawer>  component.

  @param {boolean} [show_country] - Whether to show the country selector
  @param {boolean} [show_language] - Whether to show the language selector
  @param {boolean} [country_style] - Whether to show the country flag

  @example
  {% render 'drawer-localization', country_style: true %}
{%- enddoc -%}

{% liquid
  assign background_brightness = block.settings.color_scheme.settings.background | color_brightness
  if background_brightness < 64
    assign flag_shadow_size = 4
  else
    assign flag_shadow_size = 2
  endif

  assign localization_font = '--menu-localization-font: var(--font-[localization_font]--family); ' | replace: '[localization_font]', section.settings.localization_font
  assign color_shadow = '--color-shadow: rgb(from var(--color-foreground) r g b / var(--opacity-10-25));'
  assign form_style = localization_font | append: color_shadow
%}

{% if show_language and show_country == false %}
  <div class="menu-drawer__localization color-{{ settings.popover_color_scheme }}">
    {% render 'localization-form',
      show_country: show_country,
      show_language: show_language,
      block_id: block.id,
      form_style: form_style
    %}
  </div>
{% else %}
  <drawer-localization-component class="menu-drawer__localization">
    <details
      id="drawer-localization"
      class="drawer-localization"
      on:toggle="/toggle"
    >
      <summary
        id="HeaderDrawer-localization"
        class="drawer-localization__button h3 link link--text focus-inset"
        aria-expanded="false"
        on:click="header-drawer/open"
        style="{{ form_style }}"
      >
        <div class="drawer-localization__button--label h6">
          {%- if show_country -%}
            <div class="mobile-localization mobile-localization--country link link--text">
              {%- if country_style == true -%}
                <span
                  class="icon-flag"
                  style="
                    background-image: url({{- localization.country | image_url: width: 32 }});
                    --size-shadow: {{ flag_shadow_size }}px;
                    --color-shadow: rgb(from var(--color-foreground) r g b / var(--opacity-30-60));
                  "
                ></span>
              {%- endif -%}
              <span class="currency-code">
                {{- localization.country.currency.iso_code -}}
              </span>
            </div>
          {%- endif -%}

          {%- if show_country and show_language -%}
            <span>/</span>
          {%- endif -%}

          {%- if show_language -%}
            <div class="mobile-localization mobile-localization--country link link--text">
              <span>
                {{ localization.language.iso_code | upcase }}
              </span>
            </div>
          {%- endif -%}
        </div>
        <span class="svg-wrapper icon-caret icon-caret--forward">
          {{- 'icon-caret.svg' | inline_asset_content -}}
        </span>
      </summary>

      <div
        class="menu-drawer__submenu has-submenu gradient motion-reduce color-{{ settings.drawer_color_scheme }}"
        style=""
      >
        <div
          class="menu-drawer__nav-buttons"
          ref="navButtons"
        >
          <button
            class="button menu-drawer__back-button link link--text focus-inset"
            aria-expanded="true"
            on:click="header-drawer/back"
          >
            <span class="svg-wrapper icon-caret icon-caret--backward">
              {{- 'icon-caret.svg' | inline_asset_content -}}
            </span>
            {{ 'content.localization_region_and_language' | t }}
          </button>
          <button
            class="button menu-drawer__close-button"
            type="button"
            aria-label="{{ 'actions.close' | t }}"
            on:click="header-drawer/close"
          >
            <span class="svg-wrapper header-drawer-icon header-drawer-icon--close">
              {{- 'icon-close.svg' | inline_asset_content -}}
            </span>
          </button>
        </div>

        {% render 'localization-form',
          show_country: show_country,
          show_language: show_language,
          localization_style: 'drawer',
          form_style: form_style,
          block_id: block.id
        %}
      </div>
    </details>
  </drawer-localization-component>
{% endif %}
