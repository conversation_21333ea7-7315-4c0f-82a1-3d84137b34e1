{%- doc -%}
  Renders block content for all blocks that extend the group block.

  @param {string} children - The DOM content of the group block.
  @param {object} settings - The settings of the group block.
  @param {string} shopify_attributes - String with Shopify attributes for the editor.
  @param {string} [class] - Custom classes for the group block.
  @param {string} [style] - Custom inline styles for the group block.

  @example
  {% render 'group', children: children, settings: block.settings, shopify_attributes: block.shopify_attributes %}
{%- enddoc -%}

<div
  class="
    group-block
    group-block--height-{{ settings.height }}
    group-block--width-{{ settings.width }}
    border-style
    spacing-style
    size-style
    {% if settings.inherit_color_scheme == false %} color-{{ settings.color_scheme }}{% endif %}
    {{ class }}
  "
  style="
    {% render 'border-override', settings: settings %}
    {% render 'spacing-style', settings: settings %}
    {% render 'size-style', settings: settings %}
    {{ style }}
  "
  {{ shopify_attributes }}
  data-testid="group-block"
>
  {%- if settings.link != blank -%}
    <a
      href="{{ settings.link }}"
      class="group-block__link"
      {% if settings.open_in_new_tab %}
        target="_blank" rel="noopener"
      {% endif %}
    ></a>
  {%- endif -%}

  <div class="group-block__media-wrapper">
    {% render 'background-media',
      background_media: settings.background_media,
      background_video: settings.video,
      background_video_position: settings.video_position,
      background_image: settings.background_image,
      background_image_position: settings.background_image_position
    %}
    {% if settings.toggle_overlay %}
      {% render 'overlay', settings: settings, layer: '0' %}
    {% endif %}
  </div>

  <div
    class="
      group-block-content
      {% if request.design_mode %}group-block-content--design-mode{% endif %}
      layout-panel-flex
      layout-panel-flex--{{ settings.content_direction | default: 'column' }}
      {% if settings.vertical_on_mobile %} mobile-column{% endif %}
    "
    style="{% render 'layout-panel-style', settings: settings %}"
  >
    {{- children -}}
  </div>
</div>

{% stylesheet %}
  .group-block__link {
    position: absolute;
    inset: 0;
  }

  .group-block__link ~ :is(.group-block-content, .group-block__media-wrapper) {
    pointer-events: none;
    a,
    button,
    input,
    textarea,
    select {
      pointer-events: auto;
    }
  }

  /* Needs the .group-block__link ~ to be specific enough to take effect. */
  .group-block__link ~ .group-block-content--design-mode {
    pointer-events: auto;
  }
{% endstylesheet %}
