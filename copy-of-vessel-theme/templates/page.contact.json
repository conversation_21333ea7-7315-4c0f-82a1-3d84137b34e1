/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */{
  "sections": {
    "main": {
      "type": "main-page",
      "blocks": {
        "title": {
          "type": "text",
          "name": "t:names.title",
          "settings": {
            "text": "<h2>{{ closest.page.title }}</h2>",
            "width": "100%",
            "max_width": "normal",
            "alignment": "center",
            "type_preset": "rte",
            "font": "var(--font-primary--family)",
            "font_size": "",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "color": "",
            "background": false,
            "background_color": "#00000026",
            "corner_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        },
        "content": {
          "type": "text",
          "name": "t:names.content",
          "settings": {
            "text": "{{ closest.page.content }}",
            "width": "100%",
            "max_width": "narrow",
            "alignment": "center",
            "type_preset": "rte",
            "font": "var(--font-primary--family)",
            "font_size": "",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "color": "",
            "background": false,
            "background_color": "#00000026",
            "corner_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        }
      },
      "block_order": [
        "title",
        "content"
      ],
      "settings": {
        "content_direction": "column",
        "gap": 32,
        "color_scheme": "",
        "padding-block-start": 40,
        "padding-block-end": 0
      }
    },
    "form": {
      "type": "section",
      "blocks": {
        "contact_form_UwiCkQ": {
          "type": "contact-form",
          "settings": {
            "width": "custom",
            "custom_width": 50,
            "width_mobile": "custom",
            "custom_width_mobile": 100,
            "inherit_color_scheme": true,
            "color_scheme": "",
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {
            "submit-button": {
              "type": "contact-form-submit-button",
              "static": true,
              "settings": {
                "label": "Submit",
                "style_class": "button",
                "width": "fit-content",
                "custom_width": 100,
                "width_mobile": "fit-content",
                "custom_width_mobile": 100
              },
              "blocks": {}
            }
          },
          "block_order": []
        }
      },
      "block_order": [
        "contact_form_UwiCkQ"
      ],
      "name": "t:names.contact_form",
      "settings": {
        "content_direction": "column",
        "vertical_on_mobile": true,
        "horizontal_alignment": "flex-start",
        "vertical_alignment": "center",
        "align_baseline": false,
        "horizontal_alignment_flex_direction_column": "center",
        "vertical_alignment_flex_direction_column": "center",
        "gap": 32,
        "section_width": "page-width",
        "section_height": "",
        "section_height_custom": 50,
        "color_scheme": "scheme-1",
        "background_media": "none",
        "video_position": "cover",
        "background_image_position": "cover",
        "border": "none",
        "border_width": 1,
        "border_opacity": 100,
        "border_radius": 0,
        "toggle_overlay": false,
        "overlay_color": "#00000026",
        "overlay_style": "solid",
        "gradient_direction": "to top",
        "padding-block-start": 32,
        "padding-block-end": 32
      }
    }
  },
  "order": [
    "main",
    "form"
  ]
}