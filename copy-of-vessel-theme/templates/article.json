/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */{
  "sections": {
    "section": {
      "type": "main-blog-post",
      "blocks": {
        "blog-post-title": {
          "type": "text",
          "name": "Title",
          "static": true,
          "settings": {
            "text": "<h1>{{ article.title }}</h1>",
            "width": "100%",
            "max_width": "normal",
            "alignment": "center",
            "type_preset": "h2",
            "font": "var(--font-body--family)",
            "font_size": "1rem",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "color": "var(--color-foreground)",
            "background": false,
            "background_color": "#00000026",
            "corner_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        },
        "blog-post-details": {
          "type": "_blog-post-info-text",
          "name": "Blog post details",
          "static": true,
          "settings": {
            "show_date": false,
            "show_author": false,
            "type_preset": "",
            "alignment": "center",
            "show_alignment": true,
            "padding-block-start": 24,
            "padding-block-end": 0
          },
          "blocks": {}
        },
        "blog-post-image": {
          "type": "image",
          "name": "Featured image",
          "static": true,
          "settings": {
            "image_ratio": "adapt",
            "width": "fill",
            "custom_width": 100,
            "width_mobile": "fill",
            "custom_width_mobile": 100,
            "height": "fit",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        },
        "blog-post-content": {
          "type": "_blog-post-content",
          "static": true,
          "settings": {},
          "blocks": {}
        }
      },
      "settings": {
        "content_direction": "column",
        "gap": 32,
        "color_scheme": "",
        "padding-block-start": 40,
        "padding-block-end": 80
      }
    }
  },
  "order": [
    "section"
  ]
}