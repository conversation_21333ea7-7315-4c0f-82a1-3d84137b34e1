/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */{
  "sections": {
    "main": {
      "type": "product-information",
      "blocks": {
        "media-gallery": {
          "type": "_product-media-gallery",
          "static": true,
          "settings": {
            "media_presentation": "carousel",
            "media_columns": "two",
            "image_gap": 1,
            "large_first_image": false,
            "icons_style": "arrows",
            "slideshow_controls_style": "thumbnails",
            "slideshow_mobile_controls_style": "dots",
            "thumbnail_position": "bottom",
            "thumbnail_width": 48,
            "aspect_ratio": "1",
            "media_radius": 0,
            "extend_media": false,
            "constrain_to_viewport": true,
            "zoom": true,
            "video_loop": true,
            "hide_variants": false,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        },
        "product-details": {
          "type": "_product-details",
          "static": true,
          "settings": {
            "gap": 20,
            "sticky_details_desktop": true,
            "width": "fill",
            "custom_width": 75,
            "width_mobile": "fill",
            "custom_width_mobile": 100,
            "inherit_color_scheme": true,
            "color_scheme": "",
            "background_media": "none",
            "video_position": "cover",
            "background_image_position": "cover",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 0,
            "padding-block-start": 18,
            "padding-block-end": 48,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {
            "group_JtXipy": {
              "type": "group",
              "name": "t:names.group",
              "settings": {
                "open_in_new_tab": false,
                "content_direction": "column",
                "vertical_on_mobile": true,
                "horizontal_alignment": "flex-start",
                "vertical_alignment": "center",
                "align_baseline": false,
                "horizontal_alignment_flex_direction_column": "flex-start",
                "vertical_alignment_flex_direction_column": "center",
                "gap": 28,
                "width": "fill",
                "custom_width": 65,
                "width_mobile": "fill",
                "custom_width_mobile": 100,
                "height": "fit",
                "custom_height": 100,
                "inherit_color_scheme": true,
                "color_scheme": "",
                "background_media": "none",
                "video_position": "cover",
                "background_image_position": "cover",
                "border": "none",
                "border_width": 1,
                "border_opacity": 100,
                "border_radius": 0,
                "toggle_overlay": false,
                "overlay_color": "#00000026",
                "overlay_style": "solid",
                "gradient_direction": "to top",
                "padding-block-start": 40,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {
                "group_xr9GGL": {
                  "type": "group",
                  "name": "Header",
                  "settings": {
                    "open_in_new_tab": false,
                    "content_direction": "column",
                    "vertical_on_mobile": true,
                    "horizontal_alignment": "flex-start",
                    "vertical_alignment": "flex-start",
                    "align_baseline": false,
                    "horizontal_alignment_flex_direction_column": "flex-start",
                    "vertical_alignment_flex_direction_column": "center",
                    "gap": 8,
                    "width": "fill",
                    "custom_width": 100,
                    "width_mobile": "fill",
                    "custom_width_mobile": 100,
                    "height": "fit",
                    "custom_height": 100,
                    "inherit_color_scheme": true,
                    "color_scheme": "",
                    "background_media": "none",
                    "video_position": "cover",
                    "background_image_position": "cover",
                    "border": "none",
                    "border_width": 1,
                    "border_opacity": 100,
                    "border_radius": 0,
                    "toggle_overlay": false,
                    "overlay_color": "#00000026",
                    "overlay_style": "solid",
                    "gradient_direction": "to top",
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {
                    "text_C373zj": {
                      "type": "text",
                      "settings": {
                        "text": "<h1>{{ closest.product.title }}</h1>",
                        "width": "100%",
                        "max_width": "normal",
                        "alignment": "left",
                        "type_preset": "h2",
                        "font": "var(--font-primary--family)",
                        "font_size": "",
                        "line_height": "normal",
                        "letter_spacing": "normal",
                        "case": "none",
                        "wrap": "pretty",
                        "color": "",
                        "background": false,
                        "background_color": "#00000026",
                        "corner_radius": 0,
                        "padding-block-start": 0,
                        "padding-block-end": 0,
                        "padding-inline-start": 0,
                        "padding-inline-end": 0
                      },
                      "blocks": {}
                    },
                    "group_K8TE9x": {
                      "type": "group",
                      "name": "Price & Rating",
                      "settings": {
                        "open_in_new_tab": false,
                        "content_direction": "row",
                        "vertical_on_mobile": true,
                        "horizontal_alignment": "flex-start",
                        "vertical_alignment": "flex-start",
                        "align_baseline": false,
                        "horizontal_alignment_flex_direction_column": "flex-start",
                        "vertical_alignment_flex_direction_column": "center",
                        "gap": 24,
                        "width": "fill",
                        "custom_width": 100,
                        "width_mobile": "fill",
                        "custom_width_mobile": 100,
                        "height": "fit",
                        "custom_height": 100,
                        "inherit_color_scheme": true,
                        "color_scheme": "",
                        "background_media": "none",
                        "video_position": "cover",
                        "background_image_position": "cover",
                        "border": "none",
                        "border_width": 1,
                        "border_opacity": 100,
                        "border_radius": 0,
                        "toggle_overlay": false,
                        "overlay_color": "#00000026",
                        "overlay_style": "solid",
                        "gradient_direction": "to top",
                        "padding-block-start": 0,
                        "padding-block-end": 0,
                        "padding-inline-start": 0,
                        "padding-inline-end": 0
                      },
                      "blocks": {
                        "price_WRfkDh": {
                          "type": "price",
                          "settings": {
                            "show_sale_price_first": true,
                            "show_installments": true,
                            "show_tax_info": true,
                            "type_preset": "h5",
                            "width": "100%",
                            "alignment": "left",
                            "padding-block-start": 4,
                            "padding-block-end": 0,
                            "padding-inline-start": 0,
                            "padding-inline-end": 0
                          },
                          "blocks": {}
                        },
                        "review_m3RVcq": {
                          "type": "review",
                          "settings": {
                            "stars_style": "shaded",
                            "show_number": true,
                            "rating_color": "primary",
                            "type_preset": "paragraph",
                            "alignment": "left"
                          },
                          "blocks": {}
                        }
                      },
                      "block_order": [
                        "price_WRfkDh",
                        "review_m3RVcq"
                      ]
                    }
                  },
                  "block_order": [
                    "text_C373zj",
                    "group_K8TE9x"
                  ]
                },
                "divider_grFKCP": {
                  "type": "_divider",
                  "name": "t:names.divider",
                  "settings": {
                    "thickness": 1,
                    "corner_radius": "square",
                    "width_percent": 100,
                    "padding-block-start": 0,
                    "padding-block-end": 0
                  },
                  "blocks": {}
                },
                "product_description_8gHdXj": {
                  "type": "product-description",
                  "name": "Product description",
                  "settings": {
                    "text": "<p>{{ closest.product.description }}</p>",
                    "width": "100%",
                    "max_width": "normal",
                    "alignment": "left",
                    "type_preset": "paragraph",
                    "font": "var(--font-primary--family)",
                    "font_size": "",
                    "line_height": "normal",
                    "letter_spacing": "normal",
                    "case": "none",
                    "wrap": "pretty",
                    "color": "",
                    "background": false,
                    "background_color": "#00000026",
                    "corner_radius": 0,
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {}
                },
                "variant_picker_nhbWcG": {
                  "type": "variant-picker",
                  "settings": {
                    "variant_style": "buttons",
                    "show_swatches": true,
                    "alignment": "left",
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {}
                },
                "buy_buttons_B7HMzq": {
                  "type": "buy-buttons",
                  "settings": {
                    "stacking": false,
                    "show_pickup_availability": true,
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {
                    "quantity": {
                      "type": "quantity",
                      "static": true,
                      "settings": {},
                      "blocks": {}
                    },
                    "add-to-cart": {
                      "type": "add-to-cart",
                      "static": true,
                      "settings": {
                        "style_class": "button"
                      },
                      "blocks": {}
                    },
                    "accelerated-checkout": {
                      "type": "accelerated-checkout",
                      "static": true,
                      "settings": {},
                      "blocks": {}
                    }
                  },
                  "block_order": []
                },
                "accordion_jjaG39": {
                  "type": "accordion",
                  "name": "t:names.accordion",
                  "settings": {
                    "icon": "plus",
                    "dividers": true,
                    "type_preset": "h5",
                    "inherit_color_scheme": true,
                    "color_scheme": "",
                    "border": "none",
                    "border_width": 1,
                    "border_opacity": 100,
                    "border_radius": 0,
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {
                    "accordion_row_WyAjHK": {
                      "type": "_accordion-row",
                      "settings": {
                        "heading": "CARE & MAINTENANCE",
                        "open_by_default": false
                      },
                      "blocks": {
                        "text_UfENHV": {
                          "type": "text",
                          "settings": {
                            "text": "<p>To maintain the beauty and integrity of your purchase, we recommend treating it with care. Simple maintenance practices, such as gentle washing and proper storage, can effectively preserve the longevity of your favorites. We encourage you to refer to the care instructions included with each item, designed to help you keep your purchase in top condition.</p>",
                            "width": "100%",
                            "max_width": "normal",
                            "alignment": "left",
                            "type_preset": "rte",
                            "font": "var(--font-body--family)",
                            "font_size": "1rem",
                            "line_height": "normal",
                            "letter_spacing": "normal",
                            "case": "none",
                            "wrap": "pretty",
                            "color": "var(--color-foreground)",
                            "background": false,
                            "background_color": "#00000026",
                            "corner_radius": 0,
                            "padding-block-start": 0,
                            "padding-block-end": 0,
                            "padding-inline-start": 0,
                            "padding-inline-end": 0
                          },
                          "blocks": {}
                        }
                      },
                      "block_order": [
                        "text_UfENHV"
                      ]
                    },
                    "accordion_row_xktep7": {
                      "type": "_accordion-row",
                      "settings": {
                        "heading": "SHIPPING & RETURNS",
                        "open_by_default": false
                      },
                      "blocks": {
                        "text_6rQYtf": {
                          "type": "text",
                          "settings": {
                            "text": "<p>We strive to process and ship all orders in a timely manner, working diligently to ensure that your items are on their way to you as soon as possible. Need to return something? Just let us know. </p>",
                            "width": "100%",
                            "max_width": "normal",
                            "alignment": "left",
                            "type_preset": "rte",
                            "font": "var(--font-body--family)",
                            "font_size": "1rem",
                            "line_height": "normal",
                            "letter_spacing": "normal",
                            "case": "none",
                            "wrap": "pretty",
                            "color": "var(--color-foreground)",
                            "background": false,
                            "background_color": "#00000026",
                            "corner_radius": 0,
                            "padding-block-start": 0,
                            "padding-block-end": 0,
                            "padding-inline-start": 0,
                            "padding-inline-end": 0
                          },
                          "blocks": {}
                        }
                      },
                      "block_order": [
                        "text_6rQYtf"
                      ]
                    }
                  },
                  "block_order": [
                    "accordion_row_WyAjHK",
                    "accordion_row_xktep7"
                  ]
                }
              },
              "block_order": [
                "group_xr9GGL",
                "divider_grFKCP",
                "product_description_8gHdXj",
                "variant_picker_nhbWcG",
                "buy_buttons_B7HMzq",
                "accordion_jjaG39"
              ]
            },
            "group": {
              "type": "group",
              "name": "Header",
              "disabled": true,
              "settings": {
                "open_in_new_tab": false,
                "content_direction": "row",
                "vertical_on_mobile": true,
                "horizontal_alignment": "flex-start",
                "vertical_alignment": "flex-start",
                "align_baseline": false,
                "horizontal_alignment_flex_direction_column": "flex-start",
                "vertical_alignment_flex_direction_column": "center",
                "gap": 35,
                "width": "fill",
                "custom_width": 100,
                "width_mobile": "fill",
                "custom_width_mobile": 100,
                "height": "fit",
                "custom_height": 100,
                "inherit_color_scheme": true,
                "color_scheme": "",
                "background_media": "none",
                "video_position": "cover",
                "background_image_position": "cover",
                "border": "none",
                "border_width": 1,
                "border_opacity": 100,
                "border_radius": 0,
                "toggle_overlay": false,
                "overlay_color": "#00000026",
                "overlay_style": "solid",
                "gradient_direction": "to top",
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {
                "group_Ejb3R7": {
                  "type": "group",
                  "name": "t:names.group",
                  "settings": {
                    "open_in_new_tab": false,
                    "content_direction": "column",
                    "vertical_on_mobile": true,
                    "horizontal_alignment": "flex-start",
                    "vertical_alignment": "center",
                    "align_baseline": false,
                    "horizontal_alignment_flex_direction_column": "flex-start",
                    "vertical_alignment_flex_direction_column": "center",
                    "gap": 12,
                    "width": "fit-content",
                    "custom_width": 100,
                    "width_mobile": "fill",
                    "custom_width_mobile": 100,
                    "height": "fit",
                    "custom_height": 100,
                    "inherit_color_scheme": true,
                    "color_scheme": "",
                    "background_media": "none",
                    "video_position": "cover",
                    "background_image_position": "cover",
                    "border": "none",
                    "border_width": 1,
                    "border_opacity": 100,
                    "border_radius": 0,
                    "toggle_overlay": false,
                    "overlay_color": "#00000026",
                    "overlay_style": "solid",
                    "gradient_direction": "to top",
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {
                    "text_j4RAyy": {
                      "type": "text",
                      "settings": {
                        "text": "<h1>Subtitle</h1>",
                        "width": "fit-content",
                        "max_width": "normal",
                        "alignment": "left",
                        "type_preset": "h6",
                        "font": "var(--font-primary--family)",
                        "font_size": "",
                        "line_height": "normal",
                        "letter_spacing": "normal",
                        "case": "none",
                        "wrap": "pretty",
                        "color": "",
                        "background": false,
                        "background_color": "#00000026",
                        "corner_radius": 0,
                        "padding-block-start": 0,
                        "padding-block-end": 0,
                        "padding-inline-start": 0,
                        "padding-inline-end": 0
                      },
                      "blocks": {}
                    },
                    "text_gJNTmH": {
                      "type": "text",
                      "settings": {
                        "text": "<h1>{{ closest.product.title }}</h1>",
                        "width": "100%",
                        "max_width": "normal",
                        "alignment": "left",
                        "type_preset": "h3",
                        "font": "var(--font-primary--family)",
                        "font_size": "",
                        "line_height": "normal",
                        "letter_spacing": "normal",
                        "case": "none",
                        "wrap": "pretty",
                        "color": "",
                        "background": false,
                        "background_color": "#00000026",
                        "corner_radius": 0,
                        "padding-block-start": 0,
                        "padding-block-end": 0,
                        "padding-inline-start": 0,
                        "padding-inline-end": 0
                      },
                      "blocks": {}
                    }
                  },
                  "block_order": [
                    "text_j4RAyy",
                    "text_gJNTmH"
                  ]
                },
                "text_xrnftG": {
                  "type": "text",
                  "disabled": true,
                  "settings": {
                    "text": "<h1>{{ closest.product.title }}</h1>",
                    "width": "100%",
                    "max_width": "normal",
                    "alignment": "left",
                    "type_preset": "h3",
                    "font": "var(--font-primary--family)",
                    "font_size": "",
                    "line_height": "normal",
                    "letter_spacing": "normal",
                    "case": "none",
                    "wrap": "pretty",
                    "color": "",
                    "background": false,
                    "background_color": "#00000026",
                    "corner_radius": 0,
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {}
                },
                "group_8B3NLh": {
                  "type": "group",
                  "name": "Price & Rating",
                  "settings": {
                    "open_in_new_tab": false,
                    "content_direction": "row",
                    "vertical_on_mobile": true,
                    "horizontal_alignment": "flex-start",
                    "vertical_alignment": "flex-start",
                    "align_baseline": false,
                    "horizontal_alignment_flex_direction_column": "flex-start",
                    "vertical_alignment_flex_direction_column": "center",
                    "gap": 24,
                    "width": "fill",
                    "custom_width": 100,
                    "width_mobile": "fill",
                    "custom_width_mobile": 100,
                    "height": "fit",
                    "custom_height": 100,
                    "inherit_color_scheme": true,
                    "color_scheme": "",
                    "background_media": "none",
                    "video_position": "cover",
                    "background_image_position": "cover",
                    "border": "none",
                    "border_width": 1,
                    "border_opacity": 100,
                    "border_radius": 0,
                    "toggle_overlay": false,
                    "overlay_color": "#00000026",
                    "overlay_style": "solid",
                    "gradient_direction": "to top",
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {
                    "price_97GqNF": {
                      "type": "price",
                      "settings": {
                        "show_sale_price_first": true,
                        "show_installments": true,
                        "show_tax_info": true,
                        "type_preset": "paragraph",
                        "width": "100%",
                        "alignment": "left",
                        "padding-block-start": 4,
                        "padding-block-end": 0,
                        "padding-inline-start": 0,
                        "padding-inline-end": 0
                      },
                      "blocks": {}
                    },
                    "review_QhdEwM": {
                      "type": "review",
                      "settings": {
                        "stars_style": "shaded",
                        "show_number": true,
                        "rating_color": "primary",
                        "type_preset": "paragraph",
                        "alignment": "left"
                      },
                      "blocks": {}
                    }
                  },
                  "block_order": [
                    "price_97GqNF",
                    "review_QhdEwM"
                  ]
                }
              },
              "block_order": [
                "group_Ejb3R7",
                "text_xrnftG",
                "group_8B3NLh"
              ]
            },
            "divider_MWdxWr": {
              "type": "_divider",
              "name": "t:names.divider",
              "disabled": true,
              "settings": {
                "thickness": 1,
                "corner_radius": "square",
                "width_percent": 100,
                "padding-block-start": 0,
                "padding-block-end": 0
              },
              "blocks": {}
            },
            "variant_picker": {
              "type": "variant-picker",
              "disabled": true,
              "settings": {
                "variant_style": "buttons",
                "show_swatches": true,
                "alignment": "left",
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {}
            },
            "buy_buttons": {
              "type": "buy-buttons",
              "disabled": true,
              "settings": {
                "stacking": false,
                "show_pickup_availability": true,
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {
                "quantity": {
                  "type": "quantity",
                  "disabled": true,
                  "static": true,
                  "settings": {},
                  "blocks": {}
                },
                "add-to-cart": {
                  "type": "add-to-cart",
                  "static": true,
                  "settings": {
                    "style_class": "button"
                  },
                  "blocks": {}
                },
                "accelerated-checkout": {
                  "type": "accelerated-checkout",
                  "static": true,
                  "settings": {},
                  "blocks": {}
                }
              },
              "block_order": []
            }
          },
          "block_order": [
            "group_JtXipy",
            "group",
            "divider_MWdxWr",
            "variant_picker",
            "buy_buttons"
          ]
        }
      },
      "settings": {
        "content_width": "content-center-aligned",
        "desktop_media_position": "left",
        "equal_columns": true,
        "limit_details_width": true,
        "gap": 48,
        "color_scheme": "scheme-1",
        "padding-block-start": 0,
        "padding-block-end": 20
      }
    },
    "media_with_content_kpQCTG": {
      "type": "media-with-content",
      "blocks": {
        "media": {
          "type": "_media-without-appearance",
          "static": true,
          "settings": {
            "media_type": "image",
            "video_loop": true,
            "video_autoplay": false,
            "image_position": "cover",
            "video_position": "cover"
          },
          "blocks": {}
        },
        "content": {
          "type": "_content-without-appearance",
          "static": true,
          "settings": {
            "horizontal_alignment_flex_direction_column": "center",
            "vertical_alignment_flex_direction_column": "center",
            "gap": 24
          },
          "blocks": {
            "text_jfYyLY": {
              "type": "text",
              "name": "t:names.heading",
              "settings": {
                "text": "<h2>Embracing small joys</h2>",
                "width": "fit-content",
                "max_width": "normal",
                "alignment": "left",
                "type_preset": "h3",
                "font": "var(--font-primary--family)",
                "font_size": "",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "",
                "background": false,
                "background_color": "#00000026",
                "corner_radius": 0,
                "padding-block-start": 20,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {}
            },
            "text_nnymP7": {
              "type": "text",
              "settings": {
                "text": "<p>Each item is designed to blend harmoniously with your living space while adding a unique touch. We aspire to bring balance and enrichment to everyday life.</p>",
                "width": "fit-content",
                "max_width": "narrow",
                "alignment": "left",
                "type_preset": "rte",
                "font": "var(--font-primary--family)",
                "font_size": "",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "",
                "background": false,
                "background_color": "#00000026",
                "corner_radius": 0,
                "padding-block-start": 0,
                "padding-block-end": 20,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {}
            }
          },
          "block_order": [
            "text_jfYyLY",
            "text_nnymP7"
          ]
        }
      },
      "name": "t:names.media_with_text",
      "settings": {
        "media_position": "right",
        "media_width": "medium",
        "media_height": "60svh",
        "section_width": "full-width",
        "extend_media": true,
        "color_scheme": "scheme-2",
        "padding-block-start": 0,
        "padding-block-end": 0
      }
    },
    "product-recommendations": {
      "type": "product-recommendations",
      "blocks": {
        "header": {
          "type": "text",
          "name": "t:names.header",
          "settings": {
            "text": "<h3>You may also like</h3>",
            "width": "100%",
            "max_width": "normal",
            "alignment": "center",
            "type_preset": "h3",
            "font": "var(--font-primary--family)",
            "font_size": "",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "color": "",
            "background": false,
            "background_color": "#00000026",
            "corner_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        },
        "static-product-card": {
          "type": "_product-card",
          "name": "t:names.product_card",
          "static": true,
          "settings": {
            "product_card_gap": 16,
            "inherit_color_scheme": true,
            "color_scheme": "",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {
            "card-gallery": {
              "type": "_product-card-gallery",
              "name": "t:names.product_card_media",
              "settings": {
                "image_ratio": "square",
                "border": "none",
                "border_width": 1,
                "border_opacity": 100,
                "border_radius": 0,
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {}
            },
            "group": {
              "type": "_product-card-group",
              "settings": {
                "open_in_new_tab": false,
                "content_direction": "column",
                "vertical_on_mobile": true,
                "horizontal_alignment": "flex-start",
                "vertical_alignment": "center",
                "align_baseline": false,
                "horizontal_alignment_flex_direction_column": "center",
                "vertical_alignment_flex_direction_column": "center",
                "gap": 4,
                "width": "fill",
                "custom_width": 100,
                "width_mobile": "fill",
                "custom_width_mobile": 100,
                "height": "fit",
                "custom_height": 100,
                "inherit_color_scheme": true,
                "color_scheme": "",
                "background_media": "none",
                "video_position": "cover",
                "background_image_position": "cover",
                "border": "none",
                "border_width": 1,
                "border_opacity": 100,
                "border_radius": 0,
                "toggle_overlay": false,
                "overlay_color": "#00000026",
                "overlay_style": "solid",
                "gradient_direction": "to top",
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {
                "product_title_AQqXJ9": {
                  "type": "product-title",
                  "name": "t:names.product_title",
                  "settings": {
                    "width": "fit-content",
                    "max_width": "normal",
                    "alignment": "left",
                    "type_preset": "h5",
                    "font": "var(--font-body--family)",
                    "font_size": "1rem",
                    "line_height": "normal",
                    "letter_spacing": "normal",
                    "case": "none",
                    "wrap": "pretty",
                    "color": "var(--color-foreground)",
                    "background": false,
                    "background_color": "#00000026",
                    "corner_radius": 0,
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {}
                },
                "price": {
                  "type": "price",
                  "settings": {
                    "show_sale_price_first": true,
                    "show_installments": false,
                    "show_tax_info": false,
                    "type_preset": "paragraph",
                    "width": "100%",
                    "alignment": "center",
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {}
                }
              },
              "block_order": [
                "product_title_AQqXJ9",
                "price"
              ]
            }
          },
          "block_order": [
            "card-gallery",
            "group"
          ]
        }
      },
      "block_order": [
        "header"
      ],
      "settings": {
        "product": "{{ closest.product }}",
        "recommendation_type": "related",
        "layout_type": "grid",
        "carousel_on_mobile": false,
        "max_products": 4,
        "columns": 4,
        "mobile_columns": "2",
        "columns_gap": 12,
        "rows_gap": 36,
        "icons_style": "arrow",
        "icons_shape": "none",
        "section_width": "page-width",
        "gap": 28,
        "color_scheme": "scheme-1",
        "padding-block-start": 48,
        "padding-block-end": 48
      }
    }
  },
  "order": [
    "main",
    "media_with_content_kpQCTG",
    "product-recommendations"
  ]
}