# Shopify Theme Development Setup

## Шаги для подключения к магазину клиента

### 1. Подключение к магазину
```bash
# Замените CLIENT_STORE_URL на реальный URL магазина клиента
shopify theme list --store=CLIENT_STORE_URL.myshopify.com
```

### 2. Получение списка тем
После успешного подключения вы увидите список всех тем в магазине:
- Live theme (активная тема)
- Development themes (темы в разработке)
- Unpublished themes (неопубликованные темы)

### 3. Скачивание темы в разработке
```bash
# Скачать конкретную тему по ID
shopify theme pull --store=CLIENT_STORE_URL.myshopify.com --theme-id=THEME_ID

# Или скачать тему в разработке
shopify theme pull --store=CLIENT_STORE_URL.myshopify.com --development
```

### 4. Запуск локальной разработки
```bash
# Запустить локальный сервер разработки
shopify theme dev --store=CLIENT_STORE_URL.myshopify.com
```

## Необходимые данные от клиента:
- [ ] URL магазина (например: client-store.myshopify.com)
- [ ] Права доступа к магазину (Staff account или Collaborator account)
- [ ] ID темы в разработке (если известен)

## Следующие шаги:
1. Получить URL магазина от клиента
2. Выполнить аутентификацию
3. Найти и скачать тему в разработке
4. Настроить локальную среду разработки
