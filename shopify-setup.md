# Shopify Theme Development Setup

## Шаги для подключения к магазину клиента

### 1. Подключение к магазину
```bash
# Замените CLIENT_STORE_URL на реальный URL магазина клиента
shopify theme list --store=CLIENT_STORE_URL.myshopify.com
```

### 2. Получение списка тем
После успешного подключения вы увидите список всех тем в магазине:
- Live theme (активная тема)
- Development themes (темы в разработке)
- Unpublished themes (неопубликованные темы)

### 3. Скачивание темы в разработке
```bash
# Скачать конкретную тему по ID
shopify theme pull --store=CLIENT_STORE_URL.myshopify.com --theme-id=THEME_ID

# Или скачать тему в разработке
shopify theme pull --store=CLIENT_STORE_URL.myshopify.com --development
```

### 4. Запуск локальной разработки
```bash
# Запустить локальный сервер разработки
shopify theme dev --store=CLIENT_STORE_URL.myshopify.com
```

## ✅ ВЫПОЛНЕНО:
- [x] URL магазина: upstep-custom-orthotics.myshopify.com
- [x] Shopify CLI установлен и настроен
- [x] Тема "Copy of Vessel" (ID: #152571478266) успешно скачана
- [x] Структура проекта создана в папке `copy-of-vessel-theme`

## 📋 СТАТУС ПРОЕКТА:
**Магазин:** upstep-custom-orthotics.myshopify.com
**Скачанная тема:** Copy of Vessel
**Локальная папка:** `/home/<USER>/Desktop/UPSTEP/copy-of-vessel-theme`

## 🚀 СЛЕДУЮЩИЕ ШАГИ:

### Для запуска локального сервера разработки:
```bash
cd copy-of-vessel-theme
shopify theme dev --store=upstep-custom-orthotics.myshopify.com
```
**Примечание:** Потребуется пароль магазина для подключения

### Основные команды для работы:
```bash
# Синхронизация изменений с магазином
shopify theme push --store=upstep-custom-orthotics.myshopify.com

# Получение последних изменений из магазина
shopify theme pull --store=upstep-custom-orthotics.myshopify.com

# Просмотр всех тем в магазине
shopify theme list --store=upstep-custom-orthotics.myshopify.com
```

## 📁 СТРУКТУРА ТЕМЫ:
- `assets/` - CSS, JS, изображения
- `config/` - настройки темы
- `layout/` - основные шаблоны
- `locales/` - переводы
- `sections/` - секции темы
- `snippets/` - переиспользуемые фрагменты
- `templates/` - шаблоны страниц
